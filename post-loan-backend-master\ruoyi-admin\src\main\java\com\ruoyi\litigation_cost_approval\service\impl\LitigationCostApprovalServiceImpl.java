package com.ruoyi.litigation_cost_approval.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.litigation_cost_approval.mapper.LitigationCostApprovalMapper;
import com.ruoyi.litigation_cost_approval.domain.LitigationCostApproval;
import com.ruoyi.litigation_cost_approval.service.ILitigationCostApprovalService;
import com.ruoyi.common.utils.SecurityUtils;

/**
 * 法诉费用审批Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class LitigationCostApprovalServiceImpl implements ILitigationCostApprovalService
{
    @Autowired
    private LitigationCostApprovalMapper litigationCostApprovalMapper;

    /**
     * 查询法诉费用审批列表（显示法诉记录，每个费用取最新的展示）
     * 
     * @param litigationCostApproval 法诉费用审批
     * @return 法诉费用审批
     */
    @Override
    public List<LitigationCostApproval> selectLitigationCostApprovalList(LitigationCostApproval litigationCostApproval)
    {
        return litigationCostApprovalMapper.selectLitigationCostApprovalList(litigationCostApproval);
    }

    /**
     * 查询法诉费用审批
     * 
     * @param id 法诉费用审批主键
     * @return 法诉费用审批
     */
    @Override
    public LitigationCostApproval selectLitigationCostApprovalById(Long id)
    {
        return litigationCostApprovalMapper.selectLitigationCostApprovalById(id);
    }

    /**
     * 根据法诉案件ID查询费用提交记录详情
     * 
     * @param litigationCaseId 法诉案件ID
     * @return 费用提交记录列表
     */
    @Override
    public List<LitigationCostApproval> selectSubmissionRecordsByLitigationCaseId(Long litigationCaseId)
    {
        return litigationCostApprovalMapper.selectSubmissionRecordsByLitigationCaseId(litigationCaseId);
    }

    /**
     * 新增法诉费用审批
     * 
     * @param litigationCostApproval 法诉费用审批
     * @return 结果
     */
    @Override
    public int insertLitigationCostApproval(LitigationCostApproval litigationCostApproval)
    {
        return litigationCostApprovalMapper.insertLitigationCostApproval(litigationCostApproval);
    }

    /**
     * 修改法诉费用审批
     * 
     * @param litigationCostApproval 法诉费用审批
     * @return 结果
     */
    @Override
    public int updateLitigationCostApproval(LitigationCostApproval litigationCostApproval)
    {
        return litigationCostApprovalMapper.updateLitigationCostApproval(litigationCostApproval);
    }

    /**
     * 批量删除法诉费用审批
     * 
     * @param ids 需要删除的数据主键
     * @return 结果
     */
    @Override
    public int deleteLitigationCostApprovalByIds(Long[] ids)
    {
        return litigationCostApprovalMapper.deleteLitigationCostApprovalByIds(ids);
    }

    /**
     * 删除法诉费用审批信息
     * 
     * @param id 法诉费用审批主键
     * @return 结果
     */
    @Override
    public int deleteLitigationCostApprovalById(Long id)
    {
        return litigationCostApprovalMapper.deleteLitigationCostApprovalById(id);
    }

    /**
     * 单个审批费用记录
     * 
     * @param id 记录ID
     * @param approvalStatus 审批状态
     * @param reasons 拒绝原因
     * @return 结果
     */
    @Override
    public int approveLitigationCostRecord(Long id, String approvalStatus, String reasons)
    {
        LitigationCostApproval approval = new LitigationCostApproval();
        approval.setId(id);
        approval.setApprovalStatus(approvalStatus);
        approval.setReasons(reasons);
        approval.setApproveBy(SecurityUtils.getUsername());
        approval.setApproveRole(SecurityUtils.getLoginUser().getUser().getRoles().get(0).getRoleName());

        
        return litigationCostApprovalMapper.approveLitigationCostRecord(approval);
    }

    /**
     * 批量审批费用记录
     * 
     * @param ids 记录ID列表
     * @param approvalStatus 审批状态
     * @param reasons 拒绝原因
     * @return 结果
     */
    @Override
    public int batchApproveLitigationCostRecords(List<Long> ids, String approvalStatus, String reasons)
    {
        String approveBy = SecurityUtils.getUsername();
        String approveRole = SecurityUtils.getLoginUser().getUser().getRoles().get(0).getRoleName();

        return litigationCostApprovalMapper.batchApproveLitigationCostRecords(
            ids, approvalStatus, reasons, approveBy, approveRole, new Date());
    }

    /**
     * 获取审批状态统计
     * 
     * @return 统计结果
     */
    @Override
    public List<java.util.Map<String, Object>> getApprovalStatistics()
    {
        return litigationCostApprovalMapper.getApprovalStatistics();
    }

    /**
     * 查询待审批记录数量
     * 
     * @return 待审批记录数量
     */
    @Override
    public int countPendingApproval()
    {
        return litigationCostApprovalMapper.countPendingApproval();
    }
}
