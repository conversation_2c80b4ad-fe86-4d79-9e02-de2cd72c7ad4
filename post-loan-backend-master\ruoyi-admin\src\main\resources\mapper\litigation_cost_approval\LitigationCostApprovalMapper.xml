<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.litigation_cost_approval.mapper.LitigationCostApprovalMapper">
    
    <resultMap type="LitigationCostApproval" id="LitigationCostApprovalResult">
        <result property="id"    column="id"    />
        <result property="litigationCaseId"    column="litigation_case_id"    />
        <result property="lawyerFee"    column="lawyer_fee"    />
        <result property="litigationFee"    column="litigation_fee"    />
        <result property="preservationFee"    column="preservation_fee"    />
        <result property="surveillanceFee"    column="surveillance_fee"    />
        <result property="announcementFee"    column="announcement_fee"    />
        <result property="appraisalFee"    column="appraisal_fee"    />
        <result property="executionFee"    column="execution_fee"    />
        <result property="penalty"    column="penalty"    />
        <result property="guaranteeFee"    column="guarantee_fee"    />
        <result property="intermediaryFee"    column="intermediary_fee"    />
        <result property="compensity"    column="compensity"    />
        <result property="judgmentAmount"    column="judgment_amount"    />
        <result property="specialChannelFees"    column="special_channel_fees"    />
        <result property="interest"    column="interest"    />
        <result property="otherAmountsOwed"    column="other_amounts_owed"    />
        <result property="insurance"    column="insurance"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="reasons"    column="reasons"    />
        <result property="approveBy"    column="approve_by"    />
        <result property="approveRole"    column="approve_role"    />
        <result property="applicationTime"    column="application_time"    />
        <result property="applicationBy"    column="application_by"    />
        <result property="totalMoney"    column="total_money"    />
        <result property="submissionCount"    column="submission_count"    />
        <result property="costCategory"    column="cost_category"    />
        <result property="approveTime"    column="approve_time"    />
        <!-- 关联查询字段 -->
        <result property="customerName"    column="customer_name"    />
        <result property="customerId"    column="customer_id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="certId"    column="cert_id"    />
        <result property="curator"    column="curator"    />
        <result property="courtLocation"    column="court_location"    />
        <result property="commonPleas"    column="common_pleas"    />
        <result property="jgName"    column="jg_name"    />
        <result property="area"    column="area"    />
        <result property="lendingBank"    column="lending_bank"    />
        <result property="litigationStatus"    column="litigation_status"    />
        <result property="overallApprovalStatus"    column="overall_approval_status"    />
    </resultMap>

    <sql id="selectLitigationCostApprovalVo">
        select
            -- 法诉案件基本信息
            lc.id as id,
            lc.id as litigation_case_id,
            lc.litigation_clerk as curator,
            lc.court_jurisdiction as court_location,
            lc.lawsuit_court as common_pleas,
            lc.litigation_status,
            lc.status_update_date as update_time,
            -- 贷款人信息（从loan_list获取）
            ll.customer_name,
            ll.customer_id,
            ll.apply_id,
            -- 身份证号（从customer_info获取）
            ci.cert_id,
            -- 渠道和银行信息
            so.name as jg_name,
            so.area_name as area,
            pi.org_name as lending_bank,
            -- 最新费用记录信息（直接从最新记录获取）
            lco.lawyer_fee,
            lco.litigation_fee,
            lco.preservation_fee,
            lco.surveillance_fee,
            lco.announcement_fee,
            lco.appraisal_fee,
            lco.execution_fee,
            lco.penalty,
            lco.guarantee_fee,
            lco.intermediary_fee,
            lco.compensity,
            lco.judgment_amount,
            lco.interest,
            lco.other_amounts_owed,
            lco.insurance,
            lco.total_money,
            lco.application_by,
            lco.application_time,
            lco.approve_by,
            lco.approve_role,
            lco.reasons,
            lco.approval_status,
            lco.cost_category,
            -- 提交次数统计
            (select count(*) from litigation_cost where litigation_case_id = lc.id) as submission_count,
            -- 整体审批状态计算
            (
                case
                    when (select count(*) from litigation_cost where litigation_case_id = lc.id and (approval_status is null or approval_status = '')) =
                         (select count(*) from litigation_cost where litigation_case_id = lc.id) then 'NOT_STARTED'
                    when (select count(*) from litigation_cost where litigation_case_id = lc.id and approval_status is not null and approval_status != '') =
                         (select count(*) from litigation_cost where litigation_case_id = lc.id) then 'COMPLETED'
                    else 'PARTIAL'
                end
            ) as overall_approval_status,
            -- 固定字段
            null as special_channel_fees,
            lco.approve_time
        from litigation_case lc
        -- 关联贷款信息（先通过loan_list获取基本信息）
        left join loan_list ll on lc.loan_id = ll.id
        -- 通过loan_list的apply_id关联account_loan获取org_id
        left join account_loan al on ll.apply_id = al.apply_id
        -- 关联客户信息获取身份证号
        left join customer_info ci on ll.customer_id = ci.id
        -- 关联渠道信息（使用account_loan的org_id）
        left join sys_office so on al.org_id = so.id
        -- 关联银行信息（使用account_loan的partner_id）
        left join partner_info pi on al.partner_id = pi.id
        -- 关联最新费用记录（核心优化：直接获取最新记录）
        left join litigation_cost lco on lc.id = lco.litigation_case_id
            and lco.id = (
                select id from litigation_cost
                where litigation_case_id = lc.id
                order by application_time desc
                limit 1
            )
    </sql>

    <select id="selectLitigationCostApprovalList" parameterType="LitigationCostApproval" resultMap="LitigationCostApprovalResult">
        <include refid="selectLitigationCostApprovalVo"/>
        where lco.id is not null
        <if test="litigationCaseId != null "> and lc.id = #{litigationCaseId}</if>
        <!-- 严格按照9个筛选条件 -->
        <!-- 1. 贷款人姓名 -->
        <if test="customerName != null  and customerName != ''"> and ll.customer_name like concat('%', #{customerName}, '%')</if>
        <!-- 2. 贷款人身份证号 -->
        <if test="certId != null  and certId != ''"> and ci.cert_id like concat('%', #{certId}, '%')</if>
        <!-- 3. 出单渠道 -->
        <if test="jgName != null  and jgName != ''"> and so.name like concat('%', #{jgName}, '%')</if>
        <!-- 4. 放款银行 -->
        <if test="lendingBank != null  and lendingBank != ''"> and pi.org_name like concat('%', #{lendingBank}, '%')</if>
        <!-- 5. 法诉状态(多级) -->
        <if test="litigationStatus != null  and litigationStatus != ''"> and lc.litigation_status = #{litigationStatus}</if>
        <!-- 6. 申请人 -->
        <if test="applicationBy != null  and applicationBy != ''"> and lco.application_by like concat('%', #{applicationBy}, '%')</if>
        <!-- 7. 费用类型 -->
        <if test="costCategory != null  and costCategory != ''"> and lco.cost_category = #{costCategory}</if>
        <!-- 8. 审批状态 -->
        <if test="approvalStatus != null and approvalStatus == ''"> and (lco.approval_status is null or lco.approval_status = '')</if>
        <if test="approvalStatus != null and approvalStatus != ''"> and lco.approval_status = #{approvalStatus}</if>
        <!-- 9. 申请时间区间 -->
        <if test="startTime != null and startTime != ''"> and date(lco.application_time) &gt;= #{startTime}</if>
        <if test="endTime != null and endTime != ''"> and date(lco.application_time) &lt;= #{endTime}</if>
        <!-- 10. 审批时间区间 -->
        <if test="approvalStartTime != null and approvalStartTime != ''"> and date(lco.approve_time) &gt;= #{approvalStartTime}</if>
        <if test="approvalEndTime != null and approvalEndTime != ''"> and date(lco.approve_time) &lt;= #{approvalEndTime}</if>
        order by lco.application_time desc
    </select>
    
    <select id="selectLitigationCostApprovalById" parameterType="Long" resultMap="LitigationCostApprovalResult">
        select lca.id, lca.litigation_case_id, lca.lawyer_fee, lca.litigation_fee, lca.preservation_fee,
               lca.surveillance_fee, lca.announcement_fee, lca.appraisal_fee, lca.execution_fee, lca.penalty,
               lca.guarantee_fee, lca.intermediary_fee, lca.compensity, lca.judgment_amount,
               lca.interest, lca.other_amounts_owed, lca.insurance,
               lca.approval_status, lca.reasons, lca.approve_by, lca.approve_role, lca.approve_time, lca.application_time,
               lca.application_by, lca.total_money,
               null as special_channel_fees, 1 as submission_count, null as cost_category,
               lc.litigation_clerk as curator, lc.court_jurisdiction as court_location,
               lc.lawsuit_court as common_pleas, lc.litigation_status,
               ll.customer_name, ll.customer_id, ll.apply_id, ci.cert_id, so.name as jg_name, so.area_name as area, pi.org_name as lending_bank,
               lc.status_update_date as update_time
        from litigation_cost lca
        left join litigation_case lc on lca.litigation_case_id = lc.id
        left join loan_list ll on lc.loan_id = ll.id
        left join account_loan al on ll.apply_id = al.apply_id
        left join customer_info ci on ll.customer_id = ci.id
        left join sys_office so on al.org_id = so.id
        left join partner_info pi on al.partner_id = pi.id
        where lca.id = #{id}
    </select>

    <select id="selectSubmissionRecordsByLitigationCaseId" parameterType="Long" resultMap="LitigationCostApprovalResult">
        select lca.id, lca.litigation_case_id, lca.lawyer_fee, lca.litigation_fee, lca.preservation_fee,
               lca.surveillance_fee, lca.announcement_fee, lca.appraisal_fee, lca.execution_fee, lca.penalty,
               lca.guarantee_fee, lca.intermediary_fee, lca.compensity, lca.judgment_amount,
               lca.interest, lca.other_amounts_owed, lca.insurance,
               lca.approval_status, lca.reasons, lca.approve_by, lca.approve_role, lca.approve_time, lca.application_time,
               lca.application_by, lca.total_money,
               null as special_channel_fees, 1 as submission_count, null as cost_category,
               lc.litigation_clerk as curator, lc.court_jurisdiction as court_location,
               lc.lawsuit_court as common_pleas, lc.litigation_status,
               ll.customer_name, ll.customer_id, ll.apply_id, ci.cert_id, so.name as jg_name, so.area_name as area, pi.org_name as lending_bank,
               lc.status_update_date as update_time
        from litigation_cost lca
        left join litigation_case lc on lca.litigation_case_id = lc.id
        left join loan_list ll on lc.loan_id = ll.id
        left join account_loan al on ll.apply_id = al.apply_id
        left join customer_info ci on ll.customer_id = ci.id
        left join sys_office so on al.org_id = so.id
        left join partner_info pi on al.partner_id = pi.id
        where lca.litigation_case_id = #{litigationCaseId}
        order by lca.application_time desc
    </select>
        
    <insert id="insertLitigationCostApproval" parameterType="LitigationCostApproval" useGeneratedKeys="true" keyProperty="id">
        insert into litigation_cost
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="litigationCaseId != null">litigation_case_id,</if>
            <if test="lawyerFee != null">lawyer_fee,</if>
            <if test="litigationFee != null">litigation_fee,</if>
            <if test="preservationFee != null">preservation_fee,</if>
            <if test="surveillanceFee != null">surveillance_fee,</if>
            <if test="announcementFee != null">announcement_fee,</if>
            <if test="appraisalFee != null">appraisal_fee,</if>
            <if test="executionFee != null">execution_fee,</if>
            <if test="penalty != null">penalty,</if>
            <if test="guaranteeFee != null">guarantee_fee,</if>
            <if test="intermediaryFee != null">intermediary_fee,</if>
            <if test="compensity != null">compensity,</if>
            <if test="judgmentAmount != null">judgment_amount,</if>
            <if test="interest != null">interest,</if>
            <if test="otherAmountsOwed != null">other_amounts_owed,</if>
            <if test="insurance != null">insurance,</if>
            <if test="approvalStatus != null">approval_status,</if>
            <if test="reasons != null">reasons,</if>
            <if test="approveBy != null">approve_by,</if>
            <if test="approveRole != null">approve_role,</if>
            <if test="applicationTime != null">application_time,</if>
            <if test="applicationBy != null">application_by,</if>
            <if test="totalMoney != null">total_money,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="litigationCaseId != null">#{litigationCaseId},</if>
            <if test="lawyerFee != null">#{lawyerFee},</if>
            <if test="litigationFee != null">#{litigationFee},</if>
            <if test="preservationFee != null">#{preservationFee},</if>
            <if test="surveillanceFee != null">#{surveillanceFee},</if>
            <if test="announcementFee != null">#{announcementFee},</if>
            <if test="appraisalFee != null">#{appraisalFee},</if>
            <if test="executionFee != null">#{executionFee},</if>
            <if test="penalty != null">#{penalty},</if>
            <if test="guaranteeFee != null">#{guaranteeFee},</if>
            <if test="intermediaryFee != null">#{intermediaryFee},</if>
            <if test="compensity != null">#{compensity},</if>
            <if test="judgmentAmount != null">#{judgmentAmount},</if>
            <if test="interest != null">#{interest},</if>
            <if test="otherAmountsOwed != null">#{otherAmountsOwed},</if>
            <if test="insurance != null">#{insurance},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
            <if test="reasons != null">#{reasons},</if>
            <if test="approveBy != null">#{approveBy},</if>
            <if test="approveRole != null">#{approveRole},</if>
            <if test="applicationTime != null">#{applicationTime},</if>
            <if test="applicationBy != null">#{applicationBy},</if>
            <if test="totalMoney != null">#{totalMoney},</if>
         </trim>
    </insert>

    <update id="updateLitigationCostApproval" parameterType="LitigationCostApproval">
        update litigation_cost
        <trim prefix="SET" suffixOverrides=",">
            <if test="litigationCaseId != null">litigation_case_id = #{litigationCaseId},</if>
            <if test="lawyerFee != null">lawyer_fee = #{lawyerFee},</if>
            <if test="litigationFee != null">litigation_fee = #{litigationFee},</if>
            <if test="preservationFee != null">preservation_fee = #{preservationFee},</if>
            <if test="surveillanceFee != null">surveillance_fee = #{surveillanceFee},</if>
            <if test="announcementFee != null">announcement_fee = #{announcementFee},</if>
            <if test="appraisalFee != null">appraisal_fee = #{appraisalFee},</if>
            <if test="executionFee != null">execution_fee = #{executionFee},</if>
            <if test="penalty != null">penalty = #{penalty},</if>
            <if test="guaranteeFee != null">guarantee_fee = #{guaranteeFee},</if>
            <if test="intermediaryFee != null">intermediary_fee = #{intermediaryFee},</if>
            <if test="compensity != null">compensity = #{compensity},</if>
            <if test="judgmentAmount != null">judgment_amount = #{judgmentAmount},</if>
            <if test="interest != null">interest = #{interest},</if>
            <if test="otherAmountsOwed != null">other_amounts_owed = #{otherAmountsOwed},</if>
            <if test="insurance != null">insurance = #{insurance},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="reasons != null">reasons = #{reasons},</if>
            <if test="approveBy != null">approve_by = #{approveBy},</if>
            <if test="approveRole != null">approve_role = #{approveRole},</if>
            <if test="applicationTime != null">application_time = #{applicationTime},</if>
            <if test="applicationBy != null">application_by = #{applicationBy},</if>
            <if test="totalMoney != null">total_money = #{totalMoney},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLitigationCostApprovalById" parameterType="Long">
        delete from litigation_cost where id = #{id}
    </delete>

    <delete id="deleteLitigationCostApprovalByIds" parameterType="String">
        delete from litigation_cost where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="approveLitigationCostRecord" parameterType="LitigationCostApproval">
        update litigation_cost
        set approval_status = #{approvalStatus},
            reasons = #{reasons},
            approve_by = #{approveBy},
            approve_role = #{approveRole},
            approve_time = now()
        where id = #{id}
    </update>

    <update id="batchApproveLitigationCostRecords">
        update litigation_cost
        set approval_status = #{param2},
            reasons = #{param3},
            approve_by = #{param4},
            approve_role = #{param5},
            approve_time = now()
        where id in
        <foreach item="id" collection="param1" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="getApprovalStatistics" resultType="java.util.Map">
        select 
            approval_status as status,
            count(*) as count
        from litigation_cost
        group by approval_status
    </select>

    <select id="countPendingApproval" resultType="int">
        select count(*) from litigation_cost where approval_status = '0'
    </select>

</mapper>
