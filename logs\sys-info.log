08:43:03.760 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 19508 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
08:43:03.763 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
08:43:03.768 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
08:43:06.310 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
08:43:06.312 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
08:43:06.313 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
08:43:06.399 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
08:43:08.207 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
08:43:08.928 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
08:43:11.892 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
08:43:11.900 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
08:43:11.900 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
08:43:11.901 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
08:43:11.902 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

08:43:11.902 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
08:43:11.902 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
08:43:11.902 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7d84f98
08:43:11.996 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
08:43:11.996 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
08:43:11.999 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
08:43:12.858 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
08:43:13.210 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
08:43:13.222 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.808 seconds (JVM running for 10.625)
08:43:13.224 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
08:43:13.225 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
08:43:13.225 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
08:43:13.225 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
08:43:13.228 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
08:43:13.229 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
08:43:13.229 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
08:43:13.229 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
08:43:13.229 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
08:43:13.229 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
08:45:48.080 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
08:45:48.763 [http-nio-8081-exec-1] INFO  c.r.w.c.w.WxLoginController - [wxLogin,79] - 获取到用户openid: o07uy7WkY8Q5Oxv1pjVPPWIOfAS8
08:45:48.798 [http-nio-8081-exec-1] INFO  c.r.w.c.w.WxLoginController - [wxLogin,89] - 成功解密手机号: ***********
08:45:48.846 [http-nio-8081-exec-1] INFO  c.r.w.c.w.WxLoginController - [wxLogin,158] - 手机号 *********** 未找到对应用户
08:48:16.546 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
10:39:34.722 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 15900 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:39:34.726 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:39:34.729 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:39:37.263 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:39:37.267 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:39:37.267 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:39:37.352 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:39:39.188 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:39:39.915 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:39:42.902 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:39:42.912 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:39:42.912 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:39:42.913 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:39:42.914 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:39:42.914 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:39:42.914 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:39:42.914 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@61e87312
10:39:43.015 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:39:43.016 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:39:43.019 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:39:43.901 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:39:44.216 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:39:44.225 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.861 seconds (JVM running for 10.571)
10:39:44.227 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:39:44.228 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:39:44.228 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:39:44.228 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:39:44.231 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:39:44.232 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:39:44.232 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:39:44.232 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:39:44.232 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:39:44.233 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
11:00:15.155 [http-nio-8081-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:00:19.008 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
11:02:39.862 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Logout][退出成功]
11:02:52.104 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[chudanyuan][Success][登录成功]
11:03:20.192 [schedule-pool-3] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[chudanyuan][Logout][退出成功]
11:03:22.991 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
11:03:28.077 [schedule-pool-4] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
11:07:02.040 [http-nio-8081-exec-34] INFO  c.r.w.c.w.WxLoginController - [wxLogin,79] - 获取到用户openid: o07uy7WkY8Q5Oxv1pjVPPWIOfAS8
11:07:02.040 [http-nio-8081-exec-34] INFO  c.r.w.c.w.WxLoginController - [wxLogin,167] - 用户未授权手机号，openid: o07uy7WkY8Q5Oxv1pjVPPWIOfAS8
11:09:01.882 [http-nio-8081-exec-16] INFO  c.r.w.c.w.WxLoginController - [wxLogin,79] - 获取到用户openid: o07uy7WkY8Q5Oxv1pjVPPWIOfAS8
11:09:01.886 [http-nio-8081-exec-16] INFO  c.r.w.c.w.WxLoginController - [wxLogin,89] - 成功解密手机号: ***********
11:09:01.905 [http-nio-8081-exec-16] INFO  c.r.w.c.w.WxLoginController - [wxLogin,158] - 手机号 *********** 未找到对应用户
11:11:09.207 [http-nio-8081-exec-40] INFO  c.r.w.c.w.WxLoginController - [wxLogin,79] - 获取到用户openid: o07uy7WkY8Q5Oxv1pjVPPWIOfAS8
11:11:09.208 [http-nio-8081-exec-40] INFO  c.r.w.c.w.WxLoginController - [wxLogin,89] - 成功解密手机号: ***********
11:11:09.282 [http-nio-8081-exec-40] INFO  c.r.w.c.w.WxLoginController - [wxLogin,131] - 用户 chudanyuan 微信登录成功，角色: 出单员工, token长度: 210
11:14:22.706 [schedule-pool-1] INFO  sys-user - [run,55] - [************]内网IP[yidianadmin][Success][登录成功]
11:14:31.816 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=5, loanId=6, userId=null, identity=电催员工, customerName=陈XX, customerMobile=null, carStatus=8, repaymentStatus=2, examineStatus=0, examineReason=null, bMoney=111.00, dMoney=222.00, bRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/e719f5cc-2491-4b9a-93ef-130ada39c854.jpg, dRepaymentImg=https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/218c5385-c24a-4618-8604-6340db686af2.jpg,https://yidianfile.oss-cn-hangzhou.aliyuncs.com/2025/06/25/********-e063-42ec-9337-7961ddb7af07.jpg, bAccount=345345, dAccount=345345, urgeStatus=3, urgeDescribe=123123, urgeMoney=null, appointedTime=Wed Jun 25 00:00:00 CST 2025, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=彭XX, issuingChannel=衢州华沂分公司, lendingBank=皖新租赁, alLendingBank=皖新租赁, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000006, applyId=***************************, certId=3621X51XX809X4X71X, plateNo=浙JXXJX1, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=762.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=762.57, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=1.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jun 25 13:29:07 CST 2025, updateBy=, updateTime=Tue Jul 15 20:10:29 CST 2025)
11:14:31.840 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:14:31.859 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:14:31.878 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
11:14:31.897 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:LoanReminder(id=7, loanId=2, userId=null, identity=电催员工, customerName=袁XX, customerMobile=null, carStatus=2, repaymentStatus=null, examineStatus=1, examineReason=null, bMoney=null, dMoney=null, bRepaymentImg=null, dRepaymentImg=null, bAccount=null, dAccount=null, urgeStatus=2, urgeDescribe=null, urgeMoney=null, appointedTime=null, trackingTime=null, oMoney=null, pMoney=null, pRepaymentImg=null, pAccount=null, cMoney=null, oRepaymentImg=null, cRepaymentImg=null, oAccount=null, cAccount=null, fundsRepayment=null, fundsAmount=null, fundsAccountType=null, fundsImage=null, litigationId=null, status=null, borrower=袁XX, issuingChannel=衢州华沂分公司, lendingBank=浙商银行, alLendingBank=浙商银行, alBMoney=null, alDMoney=null, bOverdueAmount=null, dOverdueAmount=null, customerId=PC0000000002, applyId=***************************, certId=3X01241X96041X03X5, plateNo=浙JXXJX2, salesman=system, jgName=衢州华沂分公司, followUp=null, bankyqMoney=3386.57, dkyqMoney=123.00, productName=测试产品, nextInstalmentAmt=1969.06, bNowMoney=1685.70, dNowMoney=820.76, bRepaymentAmounts=0.00, dRepaymentAmounts=123.00, bReturnTime=1970-01-01, dReturnTime=1970-01-01, startTime=null, endTime=null, createBy=admin, createTime=Wed Jul 09 20:34:57 CST 2025, updateBy=yidianadmin, updateTime=Tue Jul 15 21:38:28 CST 2025)
11:14:31.918 [http-nio-8081-exec-54] INFO  c.r.v.s.i.VwAccountLoanServiceImpl - [selectVwAccountLoanList,167] - reminder:null
12:49:07.816 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25504 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
12:49:07.820 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
12:49:07.821 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:49:14.756 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
12:49:14.759 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
12:49:14.759 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
12:49:14.861 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
12:49:16.761 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
12:49:17.485 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
12:49:21.227 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:49:21.236 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:49:21.236 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:49:21.237 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
12:49:21.238 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

12:49:21.238 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
12:49:21.238 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:49:21.238 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3a395f8f
12:49:21.336 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
12:49:21.337 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
12:49:21.340 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
12:49:22.271 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
12:49:22.642 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
12:49:22.652 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 15.251 seconds (JVM running for 21.935)
12:49:22.655 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
12:49:22.656 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
12:49:22.656 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
12:49:22.656 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
12:49:22.659 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
12:49:22.659 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
12:49:22.659 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
12:49:22.660 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
12:49:22.660 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
12:49:22.660 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
12:49:39.663 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:49:43.803 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
12:50:42.645 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
12:54:06.630 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 19996 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
12:54:06.633 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:54:06.633 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
12:54:09.158 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
12:54:09.161 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
12:54:09.162 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
12:54:09.252 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
12:54:11.037 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
12:54:11.744 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
12:54:14.767 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:54:14.776 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:54:14.776 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:54:14.777 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
12:54:14.778 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

12:54:14.778 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
12:54:14.779 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:54:14.779 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4aa9dbbf
12:54:14.876 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
12:54:14.876 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
12:54:14.879 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
12:54:15.684 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
12:54:16.041 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
12:54:16.051 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.761 seconds (JVM running for 10.229)
12:54:16.054 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
12:54:16.054 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
12:54:16.054 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
12:54:16.055 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
12:54:16.056 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
12:54:21.682 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:01:03.486 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 22424 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:01:03.491 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:01:03.493 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:01:05.972 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:01:05.975 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:01:05.976 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:01:06.063 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:01:07.843 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:01:08.539 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:01:11.563 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:01:11.573 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:01:11.574 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:01:11.574 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:01:11.575 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:01:11.575 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:01:11.575 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:01:11.575 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2f908183
13:01:11.664 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:01:11.665 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:01:11.668 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:01:12.547 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:01:12.915 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:01:12.924 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.774 seconds (JVM running for 10.268)
13:01:12.925 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:01:12.926 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:01:12.926 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:01:12.926 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:01:12.927 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:01:15.936 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:01:48.346 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,439] - === 单个审批方法开始 ===
13:01:48.351 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,440] - 接收到的参数: {id=12, action=approve, rejectReason=}
13:01:48.351 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,443] - 提取的ID对象: 12, 类型: Integer
13:01:48.351 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,453] - 转换后的ID: 12
13:01:48.351 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,461] - 操作类型: approve, 拒绝原因: 
13:01:48.378 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,476] - 开始查询审批记录，ID: 12
13:01:48.378 [http-nio-8081-exec-6] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,49] - === Service层查询开始 ===
13:01:48.378 [http-nio-8081-exec-6] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,50] - 查询参数ID: 12
13:01:48.410 [http-nio-8081-exec-6] INFO  c.r.l.s.i.LitigationCostApprovalServiceImpl - [selectLitigationCostApprovalById,54] - Mapper查询结果: 未找到记录
13:01:48.410 [http-nio-8081-exec-6] INFO  c.r.l.c.LitigationCostApprovalController - [singleApproveNew,478] - 查询结果: 未找到记录
13:04:26.694 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 25808 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:04:26.697 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:04:26.698 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:04:29.399 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:04:29.402 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:04:29.402 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:04:29.489 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:04:31.331 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:04:32.060 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:04:35.229 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:04:35.241 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:04:35.242 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:04:35.243 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:04:35.244 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:04:35.244 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:04:35.244 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:04:35.245 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3e388bf7
13:04:35.345 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:04:35.346 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:04:35.349 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:04:36.301 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:04:36.660 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:04:36.669 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.318 seconds (JVM running for 10.787)
13:04:36.671 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:04:36.671 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:04:36.671 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:04:36.671 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:04:36.672 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:04:36.672 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:04:36.672 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:04:36.673 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:04:36.673 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:04:36.673 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
