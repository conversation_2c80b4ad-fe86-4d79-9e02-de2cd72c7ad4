{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue?vue&type=template&id=7cd31f38&scoped=true", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue", "mtime": 1754284041743}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753353054666}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}