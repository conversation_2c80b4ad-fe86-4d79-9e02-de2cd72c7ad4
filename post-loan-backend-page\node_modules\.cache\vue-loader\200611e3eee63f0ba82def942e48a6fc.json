{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue?vue&type=template&id=7cd31f38&scoped=true", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue", "mtime": 1754277589806}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753353054666}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}