{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue", "mtime": 1754284386920}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753353053918}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBsaXN0TGl0aWdhdGlvbkNvc3RBcHByb3ZhbCwNCiAgZ2V0TGl0aWdhdGlvbkNvc3RTdWJtaXNzaW9uUmVjb3JkcywNCiAgc2luZ2xlQXBwcm92ZUxpdGlnYXRpb25Db3N0TmV3LA0KICBiYXRjaEFwcHJvdmVMaXRpZ2F0aW9uQ29zdE5ldw0KfSBmcm9tICJAL2FwaS9saXRpZ2F0aW9uX2Nvc3RfYXBwcm92YWwvbGl0aWdhdGlvbl9jb3N0X2FwcHJvdmFsIg0KaW1wb3J0IGFyZWFMaXN0IGZyb20gIi4uLy4uLy4uL2Fzc2V0cy9hcmVhLmpzb24iDQppbXBvcnQgdXNlckluZm8gZnJvbSAnQC9sYXlvdXQvY29tcG9uZW50cy9EaWFsb2cvdXNlckluZm8udnVlJw0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiVm1fY2FyX29yZGVyIiwNCiAgY29tcG9uZW50czogew0KICAgIHVzZXJJbmZvLA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8gVklFV+ihqOagvOaVsOaNrg0KICAgICAgdm1fY2FyX29yZGVyTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgLy8g5Lil5qC85oyJ54WnOeS4quetm+mAieadoeS7tg0KICAgICAgICAvLyAxLiDotLfmrL7kurrlp5PlkI0NCiAgICAgICAgY3VzdG9tZXJOYW1lOiBudWxsLA0KICAgICAgICAvLyAyLiDotLfmrL7kurrouqvku73or4Hlj7cNCiAgICAgICAgY2VydElkOiBudWxsLA0KICAgICAgICAvLyAzLiDlh7rljZXmuKDpgZMNCiAgICAgICAgamdOYW1lOiBudWxsLA0KICAgICAgICAvLyA0LiDmlL7mrL7pk7booYwNCiAgICAgICAgbGVuZGluZ0Jhbms6IG51bGwsDQogICAgICAgIC8vIDUuIOazleivieeKtuaAgSjlpJrnuqcpDQogICAgICAgIGxpdGlnYXRpb25TdGF0dXM6IG51bGwsDQogICAgICAgIC8vIDYuIOeUs+ivt+S6ug0KICAgICAgICBhcHBsaWNhdGlvbkJ5OiBudWxsLA0KICAgICAgICAvLyA3LiDotLnnlKjnsbvlnosNCiAgICAgICAgY29zdENhdGVnb3J5OiBudWxsLA0KICAgICAgICAvLyA4LiDlrqHmibnnirbmgIENCiAgICAgICAgYXBwcm92YWxTdGF0dXM6IG51bGwsDQogICAgICAgIC8vIDkuIOeUs+ivt+aXtumXtOWMuumXtA0KICAgICAgICBzdGFydFRpbWU6IG51bGwsDQogICAgICAgIGVuZFRpbWU6IG51bGwsDQogICAgICAgIC8vIDEwLiDlrqHmibnml7bpl7TljLrpl7QNCiAgICAgICAgYXBwcm92YWxTdGFydFRpbWU6IG51bGwsDQogICAgICAgIGFwcHJvdmFsRW5kVGltZTogbnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDml6XmnJ/ojIPlm7QNCiAgICAgIGRhdGVSYW5nZTogW10sDQogICAgICAvLyDlrqHmibnml6XmnJ/ojIPlm7QNCiAgICAgIGFwcHJvdmFsRGF0ZVJhbmdlOiBbXSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybTogew0KICAgICAgICBpZDonJywNCiAgICAgICAgc3RhdHVzOiAwLA0KICAgICAgICByZWplY3RSZWFzb246bnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDlvZPliY3lrqHmibnorrDlvZUNCiAgICAgIGN1cnJlbnRSZWNvcmQ6IHt9LA0KICAgICAgLy8g6LS555So5o+Q5Lqk6K6w5b2V5YiX6KGoDQogICAgICBzdWJtaXNzaW9uUmVjb3JkczogW10sDQogICAgICAvLyDorrDlvZXliqDovb3nirbmgIENCiAgICAgIHJlY29yZHNMb2FkaW5nOiBmYWxzZSwNCiAgICAgIC8vIOmAieS4reeahOiusOW9lQ0KICAgICAgc2VsZWN0ZWRSZWNvcmRzOiBbXSwNCiAgICAgIC8vIOWNleS4quWuoeaJueWvueivneahhg0KICAgICAgc2luZ2xlQXBwcm92YWxPcGVuOiBmYWxzZSwNCiAgICAgIHNpbmdsZUFwcHJvdmFsRm9ybTogew0KICAgICAgICBpZDogJycsDQogICAgICAgIGFjdGlvbjogJycsIC8vICdhcHByb3ZlJyDmiJYgJ3JlamVjdCcNCiAgICAgICAgcmVqZWN0UmVhc29uOiAnJw0KICAgICAgfSwNCiAgICAgIC8vIOaJuemHj+WuoeaJueWvueivneahhg0KICAgICAgYmF0Y2hBcHByb3ZhbE9wZW46IGZhbHNlLA0KICAgICAgYmF0Y2hBcHByb3ZhbEZvcm06IHsNCiAgICAgICAgYWN0aW9uOiAnJywgLy8gJ2FwcHJvdmUnIOaIliAncmVqZWN0Jw0KICAgICAgICByZWplY3RSZWFzb246ICcnDQogICAgICB9LA0KICAgICAgLy8g6LS35qy+5Lq65L+h5oGv55u45YWzDQogICAgICB1c2VySW5mb1Zpc2libGU6IGZhbHNlLA0KICAgICAgY3VzdG9tZXJJbmZvOiB7fSwNCiAgICAgIC8vIOazleivieeKtuaAgeWkmue6p+mAiemhuQ0KICAgICAgbGl0aWdhdGlvblN0YXR1c09wdGlvbnM6IFsNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiAn6LW36K+JJywNCiAgICAgICAgICBsYWJlbDogJ+i1t+iviScsDQogICAgICAgICAgY2hpbGRyZW46IFsNCiAgICAgICAgICAgIHsgdmFsdWU6ICfotbfor4kt5YeG5aSH5p2Q5paZJywgbGFiZWw6ICflh4blpIfmnZDmlpknIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAn6LW36K+JLeW3suaPkOS6pCcsIGxhYmVsOiAn5bey5o+Q5LqkJyB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogJ+i1t+iviS3ms5XpmaLlj5fnkIYnLCBsYWJlbDogJ+azlemZouWPl+eQhicgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiAn5a6h55CGJywNCiAgICAgICAgICBsYWJlbDogJ+WuoeeQhicsDQogICAgICAgICAgY2hpbGRyZW46IFsNCiAgICAgICAgICAgIHsgdmFsdWU6ICflrqHnkIYt5byA5bqt5a6h55CGJywgbGFiZWw6ICflvIDluq3lrqHnkIYnIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAn5a6h55CGLeetieW+heWIpOWGsycsIGxhYmVsOiAn562J5b6F5Yik5YazJyB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogJ+WuoeeQhi3kuIDlrqHliKTlhrMnLCBsYWJlbDogJ+S4gOWuoeWIpOWGsycgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiAn5omn6KGMJywNCiAgICAgICAgICBsYWJlbDogJ+aJp+ihjCcsDQogICAgICAgICAgY2hpbGRyZW46IFsNCiAgICAgICAgICAgIHsgdmFsdWU6ICfmiafooYwt55Sz6K+35omn6KGMJywgbGFiZWw6ICfnlLPor7fmiafooYwnIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAn5omn6KGMLeaJp+ihjOS4rScsIGxhYmVsOiAn5omn6KGM5LitJyB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogJ+aJp+ihjC3miafooYzlrozmr5UnLCBsYWJlbDogJ+aJp+ihjOWujOavlScgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHZhbHVlOiAn57uT5qGIJywNCiAgICAgICAgICBsYWJlbDogJ+e7k+ahiCcsDQogICAgICAgICAgY2hpbGRyZW46IFsNCiAgICAgICAgICAgIHsgdmFsdWU6ICfnu5PmoYgt6IOc6K+J57uT5qGIJywgbGFiZWw6ICfog5zor4nnu5PmoYgnIH0sDQogICAgICAgICAgICB7IHZhbHVlOiAn57uT5qGILei0peiviee7k+ahiCcsIGxhYmVsOiAn6LSl6K+J57uT5qGIJyB9LA0KICAgICAgICAgICAgeyB2YWx1ZTogJ+e7k+ahiC3lkozop6Pnu5PmoYgnLCBsYWJlbDogJ+WSjOino+e7k+ahiCcgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAga2V5UHJvdmluY2U6JycsDQogICAgICAgIGtleUNpdHk6JycsDQogICAgICAgIGtleUJvcm91Z2g6JycsDQogICAgICAgIGtleURldGFpbEFkZHJlc3M6JycsDQogICAgICB9LA0KICAgICAgcHJvdmluY2VMaXN0OmFyZWFMaXN0LA0KICAgICAgY2l0eUxpc3Q6W10sDQogICAgICBkaXN0cmljdExpc3Q6W10NCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6Lms5Xor4notLnnlKjlrqHmibnliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgbGlzdExpdGlnYXRpb25Db3N0QXBwcm92YWwodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudm1fY2FyX29yZGVyTGlzdCA9IHJlc3BvbnNlLnJvd3MNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOicnLA0KICAgICAgICBzdGF0dXM6IDAsDQogICAgICAgIHJlamVjdFJlYXNvbjpudWxsLA0KICAgICAgfQ0KICAgICAgdGhpcy5jdXJyZW50UmVjb3JkID0ge30NCiAgICAgIHRoaXMuc3VibWlzc2lvblJlY29yZHMgPSBbXQ0KICAgICAgdGhpcy5zZWxlY3RlZFJlY29yZHMgPSBbXQ0KICAgICAgdGhpcy5zaW5nbGVBcHByb3ZhbE9wZW4gPSBmYWxzZQ0KICAgICAgdGhpcy5iYXRjaEFwcHJvdmFsT3BlbiA9IGZhbHNlDQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybSA9IHsNCiAgICAgICAgaWQ6ICcnLA0KICAgICAgICBhY3Rpb246ICcnLA0KICAgICAgICByZWplY3RSZWFzb246ICcnDQogICAgICB9DQogICAgICB0aGlzLmJhdGNoQXBwcm92YWxGb3JtID0gew0KICAgICAgICBhY3Rpb246ICcnLA0KICAgICAgICByZWplY3RSZWFzb246ICcnDQogICAgICB9DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpDQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxDQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLmRhdGVSYW5nZSA9IFtdDQogICAgICB0aGlzLmFwcHJvdmFsRGF0ZVJhbmdlID0gW10NCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKQ0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpDQogICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoFZJRVciDQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5jdXJyZW50UmVjb3JkID0gcm93DQogICAgICB0aGlzLmxvYWRTdWJtaXNzaW9uUmVjb3Jkcyhyb3cubGl0aWdhdGlvbkNhc2VJZCkNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICB9LA0KDQogICAgLyoqIOaJuemHj+S/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUJhdGNoRWRpdCgpIHsNCiAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfor7fpgInmi6nljZXmnaHorrDlvZXov5vooYzlrqHmibnmk43kvZwnKQ0KICAgIH0sDQoNCiAgICAvKiog5Yqg6L296LS555So5o+Q5Lqk6K6w5b2VICovDQogICAgbG9hZFN1Ym1pc3Npb25SZWNvcmRzKGxpdGlnYXRpb25DYXNlSWQpIHsNCiAgICAgIHRoaXMucmVjb3Jkc0xvYWRpbmcgPSB0cnVlDQogICAgICBnZXRMaXRpZ2F0aW9uQ29zdFN1Ym1pc3Npb25SZWNvcmRzKGxpdGlnYXRpb25DYXNlSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLnN1Ym1pc3Npb25SZWNvcmRzID0gcmVzcG9uc2UuZGF0YSB8fCBbXQ0KICAgICAgICB0aGlzLnJlY29yZHNMb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgICAgdGhpcy5yZWNvcmRzTG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvKiog6K6w5b2V6YCJ5oup5Y+Y5YyWICovDQogICAgaGFuZGxlUmVjb3JkU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5zZWxlY3RlZFJlY29yZHMgPSBzZWxlY3Rpb24NCiAgICB9LA0KDQogICAgLyoqIOWNleS4quWuoeaJuSAqLw0KICAgIGhhbmRsZVNpbmdsZUFwcHJvdmUocmVjb3JkLCBhY3Rpb24pIHsNCiAgICAgIHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLmlkID0gcmVjb3JkLmlkDQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5hY3Rpb24gPSBhY3Rpb24NCiAgICAgIHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLnJlamVjdFJlYXNvbiA9ICcnDQogICAgICB0aGlzLnNpbmdsZUFwcHJvdmFsT3BlbiA9IHRydWUNCiAgICB9LA0KDQogICAgLyoqIOehruiupOWNleS4quWuoeaJuSAqLw0KICAgIGNvbmZpcm1TaW5nbGVBcHByb3ZhbCgpIHsNCiAgICAgIGlmICh0aGlzLnNpbmdsZUFwcHJvdmFsRm9ybS5hY3Rpb24gPT09ICdyZWplY3QnKSB7DQogICAgICAgIHRoaXMuJHJlZnNbInNpbmdsZUFwcHJvdmFsRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgICBpZiAoIXZhbGlkKSByZXR1cm4NCiAgICAgICAgICB0aGlzLmV4ZWN1dGVTaW5nbGVBcHByb3ZhbCgpDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmV4ZWN1dGVTaW5nbGVBcHByb3ZhbCgpDQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKiDmiafooYzljZXkuKrlrqHmibkgKi8NCiAgICBleGVjdXRlU2luZ2xlQXBwcm92YWwoKSB7DQogICAgICBjb25zdCBkYXRhID0gew0KICAgICAgICBpZDogdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uaWQsDQogICAgICAgIGFjdGlvbjogdGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uYWN0aW9uLA0KICAgICAgICByZWplY3RSZWFzb246IHRoaXMuc2luZ2xlQXBwcm92YWxGb3JtLnJlamVjdFJlYXNvbg0KICAgICAgfQ0KDQogICAgICBzaW5nbGVBcHByb3ZlTGl0aWdhdGlvbkNvc3ROZXcoZGF0YSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoYCR7dGhpcy5zaW5nbGVBcHByb3ZhbEZvcm0uYWN0aW9uID09PSAnYXBwcm92ZScgPyAn6YCa6L+HJyA6ICfmi5Lnu50nfeWuoeaJueaIkOWKn2ApDQogICAgICAgIHRoaXMuc2luZ2xlQXBwcm92YWxPcGVuID0gZmFsc2UNCiAgICAgICAgdGhpcy5sb2FkU3VibWlzc2lvblJlY29yZHModGhpcy5jdXJyZW50UmVjb3JkLmxpdGlnYXRpb25DYXNlSWQpDQogICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICB9KS5jYXRjaCgoZXJyb3IpID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5a6h5om55aSx6LSlOicsIGVycm9yKQ0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLyoqIOaJuemHj+WuoeaJuSAqLw0KICAgIGhhbmRsZUJhdGNoQXBwcm92ZShhY3Rpb24pIHsNCiAgICAgIGlmICh0aGlzLnNlbGVjdGVkUmVjb3Jkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+mAieaLqeimgeWuoeaJueeahOiusOW9lScpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0aGlzLmJhdGNoQXBwcm92YWxGb3JtLmFjdGlvbiA9IGFjdGlvbg0KICAgICAgdGhpcy5iYXRjaEFwcHJvdmFsRm9ybS5yZWplY3RSZWFzb24gPSAnJw0KICAgICAgdGhpcy5iYXRjaEFwcHJvdmFsT3BlbiA9IHRydWUNCiAgICB9LA0KDQogICAgLyoqIOehruiupOaJuemHj+WuoeaJuSAqLw0KICAgIGNvbmZpcm1CYXRjaEFwcHJvdmFsKCkgew0KICAgICAgaWYgKHRoaXMuYmF0Y2hBcHByb3ZhbEZvcm0uYWN0aW9uID09PSAncmVqZWN0Jykgew0KICAgICAgICB0aGlzLiRyZWZzWyJiYXRjaEFwcHJvdmFsRm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgICBpZiAoIXZhbGlkKSByZXR1cm4NCiAgICAgICAgICB0aGlzLmV4ZWN1dGVCYXRjaEFwcHJvdmFsKCkNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZXhlY3V0ZUJhdGNoQXBwcm92YWwoKQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKiog5omn6KGM5om56YeP5a6h5om5ICovDQogICAgZXhlY3V0ZUJhdGNoQXBwcm92YWwoKSB7DQogICAgICBjb25zdCBkYXRhID0gew0KICAgICAgICBpZHM6IHRoaXMuc2VsZWN0ZWRSZWNvcmRzLm1hcChyZWNvcmQgPT4gcmVjb3JkLmlkKSwNCiAgICAgICAgYWN0aW9uOiB0aGlzLmJhdGNoQXBwcm92YWxGb3JtLmFjdGlvbiwNCiAgICAgICAgcmVqZWN0UmVhc29uOiB0aGlzLmJhdGNoQXBwcm92YWxGb3JtLnJlamVjdFJlYXNvbg0KICAgICAgfQ0KDQogICAgICBiYXRjaEFwcHJvdmVMaXRpZ2F0aW9uQ29zdE5ldyhkYXRhKS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyhg5om56YePJHt0aGlzLmJhdGNoQXBwcm92YWxGb3JtLmFjdGlvbiA9PT0gJ2FwcHJvdmUnID8gJ+mAmui/hycgOiAn5ouS57udJ33lrqHmibnmiJDlip9gKQ0KICAgICAgICB0aGlzLmJhdGNoQXBwcm92YWxPcGVuID0gZmFsc2UNCiAgICAgICAgdGhpcy5zZWxlY3RlZFJlY29yZHMgPSBbXQ0KICAgICAgICB0aGlzLmxvYWRTdWJtaXNzaW9uUmVjb3Jkcyh0aGlzLmN1cnJlbnRSZWNvcmQubGl0aWdhdGlvbkNhc2VJZCkNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgIH0pLmNhdGNoKChlcnJvcikgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmibnph4/lrqHmibnlpLHotKU6JywgZXJyb3IpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvKiog5Li75YiX6KGo5om56YeP5a6h5om5ICovDQogICAgaGFuZGxlQmF0Y2hBcHByb3ZlTWFpbihzdGF0dXMpIHsNCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoJ+ivt+mAieaLqeimgeWuoeaJueeahOiusOW9lScpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICBjb25zdCBzdGF0dXNUZXh0ID0gc3RhdHVzID09PSAnMCcgPyAn6YCa6L+HJyA6ICfmi5Lnu50nDQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKGDnoa7orqTopoHmibnph48ke3N0YXR1c1RleHR96YCJ5Lit55qEICR7dGhpcy5pZHMubGVuZ3RofSDmnaHorrDlvZXlkJfvvJ9gKS50aGVuKCgpID0+IHsNCiAgICAgICAgY29uc3QgZGF0YSA9IHsNCiAgICAgICAgICBpZHM6IHRoaXMuaWRzLA0KICAgICAgICAgIGFjdGlvbjogc3RhdHVzID09PSAnMCcgPyAnYXBwcm92ZScgOiAncmVqZWN0JywNCiAgICAgICAgICByZWplY3RSZWFzb246IHN0YXR1cyA9PT0gJzEnID8gJ+aJuemHj+aLkue7nScgOiAnJw0KICAgICAgICB9DQoNCiAgICAgICAgcmV0dXJuIGJhdGNoQXBwcm92ZUxpdGlnYXRpb25Db3N0TmV3KGRhdGEpDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyhg5om56YePJHtzdGF0dXNUZXh0feaIkOWKn2ApDQogICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7fSkNCiAgICB9LA0KDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZSgpIHsNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOmAieS4reeahOaVsOaNrumhue+8nycpLnRoZW4oKCkgPT4gew0KICAgICAgICAvLyDov5nph4zlj6/ku6XosIPnlKjliKDpmaRBUEnvvIzmmoLml7blj6rmmK/mj5DnpLoNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5Yqf6IO95pqC5pyq5a6e546wIikNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQ0KICAgIH0sDQoNCg0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdsaXRpZ2F0aW9uX2Nvc3RfYXBwcm92YWwvbGl0aWdhdGlvbl9jb3N0X2FwcHJvdmFsL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYGxpdGlnYXRpb25fY29zdF9hcHByb3ZhbF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9LA0KDQogICAgLyoqIOaJk+W8gOi0t+asvuS6uuS/oeaBryAqLw0KICAgIG9wZW5Vc2VySW5mbyhyb3cpIHsNCiAgICAgIGlmICghcm93LmN1c3RvbWVySWQgJiYgIXJvdy5hcHBseUlkKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCfml6Dms5Xojrflj5botLfmrL7kurrkv6Hmga8nKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy5jdXN0b21lckluZm8gPSB7DQogICAgICAgIGN1c3RvbWVySWQ6IHJvdy5jdXN0b21lcklkLA0KICAgICAgICBhcHBseUlkOiByb3cuYXBwbHlJZCwNCiAgICAgICAgY3VzdG9tZXJOYW1lOiByb3cuY3VzdG9tZXJOYW1lDQogICAgICB9DQogICAgICB0aGlzLnVzZXJJbmZvVmlzaWJsZSA9IHRydWUNCiAgICB9LA0KDQogICAgLyoqIOWkhOeQhuaXpeacn+iMg+WbtOWPmOWMliAqLw0KICAgIGhhbmRsZURhdGVSYW5nZUNoYW5nZShkYXRlcykgew0KICAgICAgaWYgKGRhdGVzICYmIGRhdGVzLmxlbmd0aCA9PT0gMikgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0VGltZSA9IGRhdGVzWzBdDQogICAgICAgIHRoaXMucXVlcnlQYXJhbXMuZW5kVGltZSA9IGRhdGVzWzFdDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnN0YXJ0VGltZSA9IG51bGwNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5lbmRUaW1lID0gbnVsbA0KICAgICAgfQ0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCg0KICAgIC8qKiDlpITnkIblrqHmibnml7bpl7TojIPlm7Tlj5jljJYgKi8NCiAgICBoYW5kbGVBcHByb3ZhbERhdGVSYW5nZUNoYW5nZShkYXRlcykgew0KICAgICAgaWYgKGRhdGVzICYmIGRhdGVzLmxlbmd0aCA9PT0gMikgew0KICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmFwcHJvdmFsU3RhcnRUaW1lID0gZGF0ZXNbMF0NCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hcHByb3ZhbEVuZFRpbWUgPSBkYXRlc1sxXQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hcHByb3ZhbFN0YXJ0VGltZSA9IG51bGwNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5hcHByb3ZhbEVuZFRpbWUgPSBudWxsDQogICAgICB9DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KDQogICAgLyoqIOiOt+WPlueKtuaAgeaWh+acrCAqLw0KICAgIGdldFN0YXR1c1RleHQoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICcwJzogJ+acquWuoeaJuScsDQogICAgICAgICcxJzogJ+WFqOmDqOWQjOaEjycsDQogICAgICAgICcyJzogJ+W3suaLkue7nScsDQogICAgICAgICczJzogJ+azleivieS4u+euoeWuoeaJuScsDQogICAgICAgICc0JzogJ+aAu+ebkeWuoeaJuScsDQogICAgICAgICc1JzogJ+i0ouWKoeS4u+euoS/mgLvnm5HmioTpgIEnLA0KICAgICAgICAnNic6ICfmgLvnu4/nkIYv6JGj5LqL6ZW/5a6h5om5Jw0KICAgICAgfQ0KICAgICAgcmV0dXJuIHN0YXR1c01hcFtzdGF0dXNdIHx8ICfmnKrnn6XnirbmgIEnDQogICAgfSwNCg0KICAgIC8qKiDmo4Dmn6XorrDlvZXmmK/lkKblj6/ku6XlrqHmibkgKi8NCiAgICBjYW5BcHByb3ZlUmVjb3JkKHJlY29yZCkgew0KICAgICAgLy8g5qC55o2u5b2T5YmN55So5oi36KeS6Imy5ZKM6K6w5b2V54q25oCB5Yik5pat5piv5ZCm5Y+v5Lul5a6h5om5DQogICAgICAvLyDov5nph4znroDljJblpITnkIbvvIzlrp7pmYXlupTor6XmoLnmja7nlKjmiLfop5LoibLlkozlrqHmibnnirbmgIHmnaXliKTmlq0NCiAgICAgIHJldHVybiByZWNvcmQuYXBwcm92YWxTdGF0dXMgPT09ICcwJyB8fCByZWNvcmQuYXBwcm92YWxTdGF0dXMgPT09IG51bGwgfHwgcmVjb3JkLmFwcHJvdmFsU3RhdHVzID09PSAnJyB8fA0KICAgICAgICAgICAgIHJlY29yZC5hcHByb3ZhbFN0YXR1cyA9PT0gJzMnIHx8IHJlY29yZC5hcHByb3ZhbFN0YXR1cyA9PT0gJzQnIHx8DQogICAgICAgICAgICAgcmVjb3JkLmFwcHJvdmFsU3RhdHVzID09PSAnNScgfHwgcmVjb3JkLmFwcHJvdmFsU3RhdHVzID09PSAnNicNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["litigation_approval.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "litigation_approval.vue", "sourceRoot": "src/views/litigation/litigation", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <!-- 1. 贷款人姓名 -->\r\n      <el-form-item label=\"\" prop=\"customerName\">\r\n        <el-input\r\n          v-model=\"queryParams.customerName\"\r\n          placeholder=\"贷款人姓名\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 2. 贷款人身份证号 -->\r\n      <el-form-item label=\"\" prop=\"certId\">\r\n        <el-input\r\n          v-model=\"queryParams.certId\"\r\n          placeholder=\"贷款人身份证号\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 3. 出单渠道 -->\r\n      <el-form-item label=\"\" prop=\"jgName\">\r\n        <el-input\r\n          v-model=\"queryParams.jgName\"\r\n          placeholder=\"出单渠道\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 4. 放款银行 -->\r\n      <el-form-item label=\"\" prop=\"lendingBank\">\r\n        <el-input\r\n          v-model=\"queryParams.lendingBank\"\r\n          placeholder=\"放款银行\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 5. 法诉状态(需呈现多级) -->\r\n      <el-form-item label=\"\" prop=\"litigationStatus\">\r\n        <el-cascader\r\n          v-model=\"queryParams.litigationStatus\"\r\n          :options=\"litigationStatusOptions\"\r\n          :props=\"{ expandTrigger: 'hover', value: 'value', label: 'label', children: 'children' }\"\r\n          placeholder=\"法诉状态\"\r\n          clearable\r\n          @change=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 6. 申请人 -->\r\n      <el-form-item label=\"\" prop=\"applicationBy\">\r\n        <el-input\r\n          v-model=\"queryParams.applicationBy\"\r\n          placeholder=\"申请人\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 7. 费用类型 -->\r\n      <el-form-item label=\"\" prop=\"costCategory\">\r\n        <el-select v-model=\"queryParams.costCategory\" placeholder=\"费用类型\" clearable @change=\"handleQuery\">\r\n          <el-option label=\"律师费\" value=\"律师费\" />\r\n          <el-option label=\"诉讼费\" value=\"诉讼费\" />\r\n          <el-option label=\"保全费\" value=\"保全费\" />\r\n          <el-option label=\"执行费\" value=\"执行费\" />\r\n          <el-option label=\"其他费用\" value=\"其他费用\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!-- 8. 审批状态 -->\r\n      <el-form-item label=\"\" prop=\"approvalStatus\">\r\n        <el-select v-model=\"queryParams.approvalStatus\" placeholder=\"审批状态\" clearable @change=\"handleQuery\">\r\n          <el-option label=\"未审批\" value=\"0\" />\r\n          <el-option label=\"全部同意\" value=\"1\" />\r\n          <el-option label=\"已拒绝\" value=\"2\" />\r\n          <el-option label=\"法诉主管审批\" value=\"3\" />\r\n          <el-option label=\"总监审批\" value=\"4\" />\r\n          <el-option label=\"财务主管/总监抄送\" value=\"5\" />\r\n          <el-option label=\"总经理/董事长审批\" value=\"6\" />\r\n        </el-select>\r\n      </el-form-item>\r\n\r\n      <!-- 9. 申请时间区间 -->\r\n      <el-form-item label=\"\" prop=\"dateRange\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"申请开始日期\"\r\n          end-placeholder=\"申请结束日期\"\r\n          format=\"yyyy-MM-dd\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"handleDateRangeChange\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <!-- 10. 审批时间区间 -->\r\n      <el-form-item label=\"\" prop=\"approvalDateRange\">\r\n        <el-date-picker\r\n          v-model=\"approvalDateRange\"\r\n          type=\"daterange\"\r\n          range-separator=\"至\"\r\n          start-placeholder=\"审批开始日期\"\r\n          end-placeholder=\"审批结束日期\"\r\n          format=\"yyyy-MM-dd\"\r\n          value-format=\"yyyy-MM-dd\"\r\n          @change=\"handleApprovalDateRangeChange\"\r\n        />\r\n      </el-form-item>\r\n\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleBatchEdit\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['vm_car_order:vm_car_order:export']\"\r\n        >导出</el-button>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"vm_car_orderList\" @selection-change=\"handleSelectionChange\" row-key=\"id\" style=\"width: 100%\" flex=\"right\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"序号\" align=\"center\" type=\"index\" width=\"60\" fixed />\r\n      <el-table-column label=\"申请人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n      <el-table-column label=\"最新申请时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n      <el-table-column label=\"案件负责人\" align=\"center\" prop=\"curator\" width=\"100\" />\r\n      <el-table-column label=\"提交次数\" align=\"center\" prop=\"submissionCount\" width=\"100\" />\r\n      <el-table-column label=\"法诉状态\" align=\"center\" prop=\"litigationStatus\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{scope.row.litigationStatus == '1'?'待立案':'已邮寄'}}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"贷款人\" align=\"center\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            type=\"text\"\r\n            @click=\"openUserInfo(scope.row)\"\r\n            style=\"color: #409EFF;\">\r\n            {{ scope.row.customerName }}\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"出单渠道\" align=\"center\" prop=\"jgName\" />\r\n      <el-table-column label=\"地区\" align=\"center\" prop=\"area\" width=\"80\" />\r\n      <el-table-column label=\"放款银行\" align=\"center\" prop=\"lendingBank\" />\r\n      <el-table-column label=\"法院地\" align=\"center\" prop=\"courtLocation\" />\r\n      <el-table-column label=\"诉讼法院\" align=\"center\" prop=\"commonPleas\" />\r\n      <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\" />\r\n      <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\" />\r\n      <el-table-column label=\"保险费\" align=\"center\" prop=\"insurance\" width=\"80\" />\r\n      <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\" />\r\n      <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\" />\r\n      <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\" />\r\n      <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\" />\r\n      <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\" />\r\n      <el-table-column label=\"特殊通道费\" align=\"center\" prop=\"specialChannelFees\" width=\"100\" />\r\n      <el-table-column label=\"日常报销\" align=\"center\" prop=\"otherAmountsOwed\" width=\"80\" />\r\n      <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n      <el-table-column label=\"整体审批状态\" align=\"center\" prop=\"overallApprovalStatus\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.overallApprovalStatus == 'NOT_STARTED'\" type=\"info\">未开始审批</el-tag>\r\n          <el-tag v-else-if=\"scope.row.overallApprovalStatus == 'PARTIAL'\" type=\"warning\">部分审批</el-tag>\r\n          <el-tag v-else-if=\"scope.row.overallApprovalStatus == 'COMPLETED'\" type=\"success\">全部完成</el-tag>\r\n          <el-tag v-else type=\"info\">{{ scope.row.overallApprovalStatus || '未知状态' }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"当前阶段\" align=\"center\" prop=\"approvalStatus\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag v-if=\"scope.row.approvalStatus == '0' || scope.row.approvalStatus == null || scope.row.approvalStatus == ''\" type=\"info\" size=\"mini\">未审批</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '1'\" type=\"success\" size=\"mini\">全部同意</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '2'\" type=\"danger\" size=\"mini\">已拒绝</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '3'\" type=\"warning\" size=\"mini\">法诉主管审批</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '4'\" type=\"warning\" size=\"mini\">总监审批</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '5'\" type=\"warning\" size=\"mini\">财务主管/总监抄送</el-tag>\r\n          <el-tag v-else-if=\"scope.row.approvalStatus == '6'\" type=\"warning\" size=\"mini\">总经理/董事长审批</el-tag>\r\n          <el-tag v-else type=\"info\" size=\"mini\">{{ getStatusText(scope.row.approvalStatus) }}</el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\" />\r\n      <el-table-column label=\"审批人角色\" align=\"center\" prop=\"approveRole\" />\r\n      <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" />\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"100\" class-name=\"small-padding fixed-width\" fixed=\"right\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['vm_car_order:vm_car_order:edit']\"\r\n          >审批</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 审批对话框 -->\r\n    <el-dialog title=\"法诉费用审批详情\" :visible.sync=\"open\" width=\"1200px\" append-to-body>\r\n      <div class=\"approval-header\">\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <strong>贷款人：</strong>{{ currentRecord.customerName }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>案件负责人：</strong>{{ currentRecord.curator }}\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <strong>法院地：</strong>{{ currentRecord.courtLocation }}\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <div class=\"batch-approval-section\" style=\"margin: 20px 0;\">\r\n        <el-button\r\n          type=\"success\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('approve')\">\r\n          批量通过 ({{ selectedRecords.length }})\r\n        </el-button>\r\n        <el-button\r\n          type=\"danger\"\r\n          size=\"small\"\r\n          :disabled=\"selectedRecords.length === 0\"\r\n          @click=\"handleBatchApprove('reject')\">\r\n          批量拒绝 ({{ selectedRecords.length }})\r\n        </el-button>\r\n      </div>\r\n\r\n      <el-table\r\n        :data=\"submissionRecords\"\r\n        @selection-change=\"handleRecordSelectionChange\"\r\n        v-loading=\"recordsLoading\">\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" fixed=\"left\" />\r\n        <el-table-column label=\"提交时间\" align=\"center\" prop=\"applicationTime\" width=\"150\" />\r\n        <el-table-column label=\"提交人\" align=\"center\" prop=\"applicationBy\" width=\"100\" />\r\n        <el-table-column label=\"律师费\" align=\"center\" prop=\"lawyerFee\" width=\"80\" />\r\n        <el-table-column label=\"诉讼费\" align=\"center\" prop=\"litigationFee\" width=\"80\" />\r\n        <el-table-column label=\"保全费\" align=\"center\" prop=\"preservationFee\" width=\"80\" />\r\n        <el-table-column label=\"布控费\" align=\"center\" prop=\"surveillanceFee\" width=\"80\" />\r\n        <el-table-column label=\"公告费\" align=\"center\" prop=\"announcementFee\" width=\"80\" />\r\n        <el-table-column label=\"评估费\" align=\"center\" prop=\"appraisalFee\" width=\"80\" />\r\n        <el-table-column label=\"执行费\" align=\"center\" prop=\"executionFee\" width=\"80\" />\r\n        <el-table-column label=\"违约金\" align=\"center\" prop=\"penalty\" width=\"80\" />\r\n        <el-table-column label=\"担保费\" align=\"center\" prop=\"guaranteeFee\" width=\"80\" />\r\n        <el-table-column label=\"居间费\" align=\"center\" prop=\"intermediaryFee\" width=\"80\" />\r\n        <el-table-column label=\"代偿金\" align=\"center\" prop=\"compensity\" width=\"80\" />\r\n        <el-table-column label=\"判决金额\" align=\"center\" prop=\"judgmentAmount\" width=\"100\" />\r\n        <el-table-column label=\"利息\" align=\"center\" prop=\"interest\" width=\"80\" />\r\n        <el-table-column label=\"其他欠款\" align=\"center\" prop=\"otherAmountsOwed\" width=\"100\" />\r\n        <el-table-column label=\"保险费\" align=\"center\" prop=\"insurance\" width=\"80\" />\r\n        <el-table-column label=\"总费用\" align=\"center\" prop=\"totalMoney\" width=\"100\" />\r\n        <el-table-column label=\"审批状态\" align=\"center\" prop=\"approvalStatus\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-tag v-if=\"scope.row.approvalStatus == '0' || scope.row.approvalStatus == null || scope.row.approvalStatus == ''\" type=\"info\">未审批</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '1'\" type=\"success\">全部同意</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '2'\" type=\"danger\">已拒绝</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '3'\" type=\"warning\">法诉主管审批</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '4'\" type=\"warning\">总监审批</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '5'\" type=\"warning\">财务主管/总监抄送</el-tag>\r\n            <el-tag v-else-if=\"scope.row.approvalStatus == '6'\" type=\"warning\">总经理/董事长审批</el-tag>\r\n            <el-tag v-else type=\"warning\">{{ getStatusText(scope.row.approvalStatus) }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"审批时间\" align=\"center\" prop=\"approveTime\" width=\"150\" />\r\n        <el-table-column label=\"审批人\" align=\"center\" prop=\"approveBy\" width=\"100\" />\r\n        <el-table-column label=\"拒绝原因\" align=\"center\" prop=\"reasons\" width=\"150\" />\r\n        <el-table-column label=\"操作\" align=\"center\" width=\"150\" fixed=\"right\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleSingleApprove(scope.row, 'approve')\">\r\n              通过\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"canApproveRecord(scope.row)\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleSingleApprove(scope.row, 'reject')\">\r\n              拒绝\r\n            </el-button>\r\n            <div v-else>\r\n              <el-tag v-if=\"scope.row.approvalStatus == '1'\" type=\"success\" size=\"mini\">全部同意</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '2'\" type=\"danger\" size=\"mini\">已拒绝</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '3'\" type=\"warning\" size=\"mini\">法诉主管审批</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '4'\" type=\"warning\" size=\"mini\">总监审批</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '5'\" type=\"warning\" size=\"mini\">财务主管/总监抄送</el-tag>\r\n              <el-tag v-else-if=\"scope.row.approvalStatus == '6'\" type=\"warning\" size=\"mini\">总经理/董事长审批</el-tag>\r\n              <div v-if=\"scope.row.approveBy\" style=\"font-size: 12px; color: #999; margin-top: 2px;\">\r\n                {{ scope.row.approveBy }}\r\n              </div>\r\n              <div v-if=\"scope.row.approveTime\" style=\"font-size: 12px; color: #999;\">\r\n                {{ scope.row.approveTime }}\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"cancel\">关 闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 单个审批确认对话框 -->\r\n    <el-dialog title=\"审批确认\" :visible.sync=\"singleApprovalOpen\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"singleApprovalForm\" :model=\"singleApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"singleApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ singleApprovalForm.action === 'approve' ? '通过' : '拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"singleApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"singleApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmSingleApproval\">确 定</el-button>\r\n        <el-button @click=\"singleApprovalOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 批量审批确认对话框 -->\r\n    <el-dialog title=\"批量审批确认\" :visible.sync=\"batchApprovalOpen\" width=\"400px\" append-to-body>\r\n      <el-form ref=\"batchApprovalForm\" :model=\"batchApprovalForm\" label-width=\"80px\">\r\n        <el-form-item label=\"审批结果\">\r\n          <el-tag :type=\"batchApprovalForm.action === 'approve' ? 'success' : 'danger'\">\r\n            {{ batchApprovalForm.action === 'approve' ? '批量通过' : '批量拒绝' }}\r\n          </el-tag>\r\n        </el-form-item>\r\n        <el-form-item label=\"选中记录\">\r\n          <span>{{ selectedRecords.length }} 条记录</span>\r\n        </el-form-item>\r\n        <el-form-item\r\n          label=\"拒绝原因\"\r\n          prop=\"rejectReason\"\r\n          v-if=\"batchApprovalForm.action === 'reject'\"\r\n          :rules=\"[{ required: true, message: '请输入拒绝原因', trigger: 'blur' }]\">\r\n          <el-input\r\n            v-model=\"batchApprovalForm.rejectReason\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入拒绝原因\"\r\n          ></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"confirmBatchApproval\">确 定</el-button>\r\n        <el-button @click=\"batchApprovalOpen = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 贷款人信息对话框 -->\r\n    <userInfo ref=\"userInfo\" :visible.sync=\"userInfoVisible\" title=\"贷款人信息\" :customerInfo=\"customerInfo\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  listLitigationCostApproval,\r\n  getLitigationCostSubmissionRecords,\r\n  singleApproveLitigationCostNew,\r\n  batchApproveLitigationCostNew\r\n} from \"@/api/litigation_cost_approval/litigation_cost_approval\"\r\nimport areaList from \"../../../assets/area.json\"\r\nimport userInfo from '@/layout/components/Dialog/userInfo.vue'\r\nexport default {\r\n  name: \"Vm_car_order\",\r\n  components: {\r\n    userInfo,\r\n  },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // VIEW表格数据\r\n      vm_car_orderList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        // 严格按照9个筛选条件\r\n        // 1. 贷款人姓名\r\n        customerName: null,\r\n        // 2. 贷款人身份证号\r\n        certId: null,\r\n        // 3. 出单渠道\r\n        jgName: null,\r\n        // 4. 放款银行\r\n        lendingBank: null,\r\n        // 5. 法诉状态(多级)\r\n        litigationStatus: null,\r\n        // 6. 申请人\r\n        applicationBy: null,\r\n        // 7. 费用类型\r\n        costCategory: null,\r\n        // 8. 审批状态\r\n        approvalStatus: null,\r\n        // 9. 申请时间区间\r\n        startTime: null,\r\n        endTime: null,\r\n        // 10. 审批时间区间\r\n        approvalStartTime: null,\r\n        approvalEndTime: null,\r\n      },\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 审批日期范围\r\n      approvalDateRange: [],\r\n      // 表单参数\r\n      form: {\r\n        id:'',\r\n        status: 0,\r\n        rejectReason:null,\r\n      },\r\n      // 当前审批记录\r\n      currentRecord: {},\r\n      // 费用提交记录列表\r\n      submissionRecords: [],\r\n      // 记录加载状态\r\n      recordsLoading: false,\r\n      // 选中的记录\r\n      selectedRecords: [],\r\n      // 单个审批对话框\r\n      singleApprovalOpen: false,\r\n      singleApprovalForm: {\r\n        id: '',\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 批量审批对话框\r\n      batchApprovalOpen: false,\r\n      batchApprovalForm: {\r\n        action: '', // 'approve' 或 'reject'\r\n        rejectReason: ''\r\n      },\r\n      // 贷款人信息相关\r\n      userInfoVisible: false,\r\n      customerInfo: {},\r\n      // 法诉状态多级选项\r\n      litigationStatusOptions: [\r\n        {\r\n          value: '起诉',\r\n          label: '起诉',\r\n          children: [\r\n            { value: '起诉-准备材料', label: '准备材料' },\r\n            { value: '起诉-已提交', label: '已提交' },\r\n            { value: '起诉-法院受理', label: '法院受理' }\r\n          ]\r\n        },\r\n        {\r\n          value: '审理',\r\n          label: '审理',\r\n          children: [\r\n            { value: '审理-开庭审理', label: '开庭审理' },\r\n            { value: '审理-等待判决', label: '等待判决' },\r\n            { value: '审理-一审判决', label: '一审判决' }\r\n          ]\r\n        },\r\n        {\r\n          value: '执行',\r\n          label: '执行',\r\n          children: [\r\n            { value: '执行-申请执行', label: '申请执行' },\r\n            { value: '执行-执行中', label: '执行中' },\r\n            { value: '执行-执行完毕', label: '执行完毕' }\r\n          ]\r\n        },\r\n        {\r\n          value: '结案',\r\n          label: '结案',\r\n          children: [\r\n            { value: '结案-胜诉结案', label: '胜诉结案' },\r\n            { value: '结案-败诉结案', label: '败诉结案' },\r\n            { value: '结案-和解结案', label: '和解结案' }\r\n          ]\r\n        }\r\n      ],\r\n      // 表单校验\r\n      rules: {\r\n        keyProvince:'',\r\n        keyCity:'',\r\n        keyBorough:'',\r\n        keyDetailAddress:'',\r\n      },\r\n      provinceList:areaList,\r\n      cityList:[],\r\n      districtList:[]\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    /** 查询法诉费用审批列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listLitigationCostApproval(this.queryParams).then(response => {\r\n        this.vm_car_orderList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id:'',\r\n        status: 0,\r\n        rejectReason:null,\r\n      }\r\n      this.currentRecord = {}\r\n      this.submissionRecords = []\r\n      this.selectedRecords = []\r\n      this.singleApprovalOpen = false\r\n      this.batchApprovalOpen = false\r\n      this.singleApprovalForm = {\r\n        id: '',\r\n        action: '',\r\n        rejectReason: ''\r\n      }\r\n      this.batchApprovalForm = {\r\n        action: '',\r\n        rejectReason: ''\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = []\r\n      this.approvalDateRange = []\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加VIEW\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.currentRecord = row\r\n      this.loadSubmissionRecords(row.litigationCaseId)\r\n      this.open = true\r\n    },\r\n\r\n    /** 批量修改按钮操作 */\r\n    handleBatchEdit() {\r\n      this.$modal.msgError('请选择单条记录进行审批操作')\r\n    },\r\n\r\n    /** 加载费用提交记录 */\r\n    loadSubmissionRecords(litigationCaseId) {\r\n      this.recordsLoading = true\r\n      getLitigationCostSubmissionRecords(litigationCaseId).then(response => {\r\n        this.submissionRecords = response.data || []\r\n        this.recordsLoading = false\r\n      }).catch(() => {\r\n        this.recordsLoading = false\r\n      })\r\n    },\r\n\r\n    /** 记录选择变化 */\r\n    handleRecordSelectionChange(selection) {\r\n      this.selectedRecords = selection\r\n    },\r\n\r\n    /** 单个审批 */\r\n    handleSingleApprove(record, action) {\r\n      this.singleApprovalForm.id = record.id\r\n      this.singleApprovalForm.action = action\r\n      this.singleApprovalForm.rejectReason = ''\r\n      this.singleApprovalOpen = true\r\n    },\r\n\r\n    /** 确认单个审批 */\r\n    confirmSingleApproval() {\r\n      if (this.singleApprovalForm.action === 'reject') {\r\n        this.$refs[\"singleApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeSingleApproval()\r\n        })\r\n      } else {\r\n        this.executeSingleApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行单个审批 */\r\n    executeSingleApproval() {\r\n      const data = {\r\n        id: this.singleApprovalForm.id,\r\n        action: this.singleApprovalForm.action,\r\n        rejectReason: this.singleApprovalForm.rejectReason\r\n      }\r\n\r\n      singleApproveLitigationCostNew(data).then(() => {\r\n        this.$modal.msgSuccess(`${this.singleApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.singleApprovalOpen = false\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        console.error('审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 批量审批 */\r\n    handleBatchApprove(action) {\r\n      if (this.selectedRecords.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      this.batchApprovalForm.action = action\r\n      this.batchApprovalForm.rejectReason = ''\r\n      this.batchApprovalOpen = true\r\n    },\r\n\r\n    /** 确认批量审批 */\r\n    confirmBatchApproval() {\r\n      if (this.batchApprovalForm.action === 'reject') {\r\n        this.$refs[\"batchApprovalForm\"].validate(valid => {\r\n          if (!valid) return\r\n          this.executeBatchApproval()\r\n        })\r\n      } else {\r\n        this.executeBatchApproval()\r\n      }\r\n    },\r\n\r\n    /** 执行批量审批 */\r\n    executeBatchApproval() {\r\n      const data = {\r\n        ids: this.selectedRecords.map(record => record.id),\r\n        action: this.batchApprovalForm.action,\r\n        rejectReason: this.batchApprovalForm.rejectReason\r\n      }\r\n\r\n      batchApproveLitigationCostNew(data).then(() => {\r\n        this.$modal.msgSuccess(`批量${this.batchApprovalForm.action === 'approve' ? '通过' : '拒绝'}审批成功`)\r\n        this.batchApprovalOpen = false\r\n        this.selectedRecords = []\r\n        this.loadSubmissionRecords(this.currentRecord.litigationCaseId)\r\n        this.getList()\r\n      }).catch((error) => {\r\n        console.error('批量审批失败:', error)\r\n      })\r\n    },\r\n\r\n    /** 主列表批量审批 */\r\n    handleBatchApproveMain(status) {\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError('请选择要审批的记录')\r\n        return\r\n      }\r\n\r\n      const statusText = status === '0' ? '通过' : '拒绝'\r\n      this.$modal.confirm(`确认要批量${statusText}选中的 ${this.ids.length} 条记录吗？`).then(() => {\r\n        const data = {\r\n          ids: this.ids,\r\n          action: status === '0' ? 'approve' : 'reject',\r\n          rejectReason: status === '1' ? '批量拒绝' : ''\r\n        }\r\n\r\n        return batchApproveLitigationCostNew(data)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(`批量${statusText}成功`)\r\n        this.getList()\r\n      }).catch(() => {})\r\n    },\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete() {\r\n      this.$modal.confirm('是否确认删除选中的数据项？').then(() => {\r\n        // 这里可以调用删除API，暂时只是提示\r\n        this.$modal.msgSuccess(\"删除功能暂未实现\")\r\n      }).catch(() => {})\r\n    },\r\n\r\n\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('litigation_cost_approval/litigation_cost_approval/export', {\r\n        ...this.queryParams\r\n      }, `litigation_cost_approval_${new Date().getTime()}.xlsx`)\r\n    },\r\n\r\n    /** 打开贷款人信息 */\r\n    openUserInfo(row) {\r\n      if (!row.customerId && !row.applyId) {\r\n        this.$modal.msgError('无法获取贷款人信息')\r\n        return\r\n      }\r\n\r\n      this.customerInfo = {\r\n        customerId: row.customerId,\r\n        applyId: row.applyId,\r\n        customerName: row.customerName\r\n      }\r\n      this.userInfoVisible = true\r\n    },\r\n\r\n    /** 处理日期范围变化 */\r\n    handleDateRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.queryParams.startTime = dates[0]\r\n        this.queryParams.endTime = dates[1]\r\n      } else {\r\n        this.queryParams.startTime = null\r\n        this.queryParams.endTime = null\r\n      }\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 处理审批时间范围变化 */\r\n    handleApprovalDateRangeChange(dates) {\r\n      if (dates && dates.length === 2) {\r\n        this.queryParams.approvalStartTime = dates[0]\r\n        this.queryParams.approvalEndTime = dates[1]\r\n      } else {\r\n        this.queryParams.approvalStartTime = null\r\n        this.queryParams.approvalEndTime = null\r\n      }\r\n      this.handleQuery()\r\n    },\r\n\r\n    /** 获取状态文本 */\r\n    getStatusText(status) {\r\n      const statusMap = {\r\n        '0': '未审批',\r\n        '1': '全部同意',\r\n        '2': '已拒绝',\r\n        '3': '法诉主管审批',\r\n        '4': '总监审批',\r\n        '5': '财务主管/总监抄送',\r\n        '6': '总经理/董事长审批'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    },\r\n\r\n    /** 检查记录是否可以审批 */\r\n    canApproveRecord(record) {\r\n      // 根据当前用户角色和记录状态判断是否可以审批\r\n      // 这里简化处理，实际应该根据用户角色和审批状态来判断\r\n      return record.approvalStatus === '0' || record.approvalStatus === null || record.approvalStatus === '' ||\r\n             record.approvalStatus === '3' || record.approvalStatus === '4' ||\r\n             record.approvalStatus === '5' || record.approvalStatus === '6'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.approval-header {\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.batch-approval-section {\r\n  border: 1px solid #e4e7ed;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.el-table {\r\n  margin-top: 10px;\r\n}\r\n\r\n.el-tag {\r\n  margin: 2px;\r\n}\r\n</style>\r\n"]}]}